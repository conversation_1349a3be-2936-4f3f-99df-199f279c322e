# Telegram MCP Client Architecture

## General Structure

The project is built using a session-based modular architecture and supports single-user conversation contexts including private chats, channel discussions, and group chats. Each channel/chat is mapped to a specific admin user for access control.

```
telegram-mcp-client/
├── main.py                # Application entry point
├── src/                   # Source code
│   ├── ai/                # AI components (Agno-based)
│   ├── messaging/         # Message processing and formatting
│   ├── telegram/          # Telegram API integration
│   ├── utils/             # Utilities (config, whitelist, sessions)
│   └── __init__.py
└── requirements.txt       # Project dependencies
```

## Main Components

### 1. AI Module (`src/ai/`)

Responsible for interacting with Claude AI via the Agno library, processing requests, and generating responses.

- **`agent.py`** - main `AIAgent` class that initializes the AI model and tools
- **`query_processor.py`** - request processor managing text streaming and tool integration
- **`summarizer.py`** - memory management and session summarization
- **`types.py`** - data type definitions for the AI module

### 2. Messaging Module (`src/messaging/`)

Handles messages, their formatting, and delivery to Telegram.

- **`formatter.py`** - formats messages for display in Telegram
- **`processor.py`** - `MessageProcessor` class for managing message sending and updates
- **`components.py`** - message components (text, tools, etc.)
- **`splitter.py`** - splits long messages into parts that don't exceed Telegram limits
- **`telegram_adapter.py`** - adapter for interacting with the Telegram API

### 3. Telegram Module (`src/telegram/`)

Manages the Telegram bot, processes events, and routes messages to the AI.

- **`bot.py`** - main bot class handling all message types and contexts
- **`commands/`** - command handlers (start, clear, clear_memory)
- **`handlers/`** - universal message handler supporting all chat types

### 4. Utilities (`src/utils/`)

Helper functions for configuration, authorization, and session management.

- **`config.py`** - configuration and whitelist definitions
- **`whitelist.py`** - authorization checking for users/channels/chats
- **`session.py`** - session management system
- **`logging.py`** - logging configuration
- **`speech_to_text.py`** - voice message transcription
- **`helpers.py`** - miscellaneous utilities

## Session Management

### Session Types

The application supports three types of sessions with admin-based access control:

1. **Private Sessions** (`private_{user_id}`)
   - One session per user
   - Direct one-on-one conversations

2. **Channel Discussion Sessions** (`channel_discussion_{chat_id}_post_{original_post_id}`)
   - Each channel post creates a separate session using the original post ID
   - All comments/replies to the same post share the same session
   - Bot always replies to the original post to maintain thread context
   - Multiple concurrent sessions per channel (one per original post)
   - **Single-user mode**: All interactions use the mapped admin's user_id

3. **Chat Sessions** (`chat_{chat_id}_{user_id}`)
   - One session per user in group chats
   - Isolated conversations per user

### Session Context

Each session maintains:
- **Session ID** - unique identifier
- **Session Type** - private/channel/chat
- **User ID** - requesting user
- **Chat ID** - conversation context
- **Thread ID** - for channel posts (optional)

## Authorization System

### Whitelist Configuration

Four levels of authorization in `config.py`:

1. **`WHITELIST_USERS`** - Authorized user IDs (works across all contexts)
2. **`WHITELIST_CHANNELS`** - Authorized channel IDs
3. **`WHITELIST_CHANNEL_CHATS`** - Authorized group chat IDs
4. **`CHANNEL_ADMIN_MAP`** - Maps channel/chat IDs to admin user IDs for single-user operation

### Authorization Logic

- Users in `WHITELIST_USERS` can use the bot anywhere
- Channel-specific authorization via `WHITELIST_CHANNELS`
- Group chat authorization via `WHITELIST_CHANNEL_CHATS`
- **Admin mapping**: Channel/chat interactions use mapped admin user_id from `CHANNEL_ADMIN_MAP`
- Special handling for automatic forwards (user_id 777000) from channels
- Unauthorized access is logged and blocked

## Main Data Flows

1. **Receiving a message:**
   - Telegram Bot receives message from any context
   - Authorization check via whitelist
   - Session context determination
   - Route to appropriate AI agent

2. **Session-based processing:**
   - Session manager creates/retrieves session context
   - AI agent specific to session handles request
   - Memory and history isolated per session

3. **Response handling:**
   - Context-aware reply targeting and session management
   - **Channel discussions**: Always reply to the original post (maintains thread context)
     - New channel post → Creates new session, bot replies to this post
     - Comment on post → Uses existing session, bot replies to original post
     - Uses admin's user_id for all interactions (single-user mode)
   - **Group chats**: Reply to user's specific message
   - **Private chats**: Direct response (no reply targeting needed)

4. **Message formatting and delivery:**
   - Universal message processing
   - Intelligent message splitting
   - Real-time streaming updates

## Implementation Features

### AI Integration

- **Agno-based architecture**: Uses Agno library for multi-user, multi-session conversations
- **Session isolation**: Each session maintains separate conversation context
- **Memory management**: User-specific memories and session summaries
- **Static agent ID**: Single agent instance ("telegram_mcp_client_agent") handles all sessions

### Message Processing

- Implemented streaming message updates at different speeds:
  - Fast updates for the first ~150 characters (0.5 seconds)
  - Slower updates for the rest of the message (2 seconds)

### Message Separation

The project uses smart message splitting via the `MessageSplitter` class, which finds optimal splitting points:

1. First tries to split at paragraph boundaries ("\n\n\n")
2. Then at sentence boundaries (after ".!?")
3. Then at word boundaries (spaces)
4. In the extreme case, separates exactly at the maximum length

### Handling Tools

- Visualize the start and end of tools
- Track the status of each tool in the response stream
- Prevent duplicate tool processing

### Voice Message Support

- Automatic transcription using Deepgram API
- Support for both voice and audio messages
- Language detection and processing

### Multi-Context Support

- **Private Chats**: Traditional one-on-one conversations
- **Channel Discussions**: Post-based sessions with admin-controlled access
- **Group Chats**: User-isolated sessions within shared spaces
- **Single-user channel mode**: All channel interactions use mapped admin's identity

### Configuration

The basic configuration is in `src/utils/config.py`:

- Maximum message length (standard 4096 characters)
- Timeouts and limits for API calls
- Claude model and other AI parameters
- Whitelist definitions for access control

## Startup and Shutdown

- Main loop is started in `main.py`
- Correct signal handling for safe shutdown of the bot
- Logging of major events for debugging
- Session persistence across restarts

## Usage Examples

### Adding to Whitelist

```python
# config.py
WHITELIST_USERS = {
    123456789,  # Your user ID
}

WHITELIST_CHANNELS = {
    -1001234567890,  # Channel ID
}

WHITELIST_CHANNEL_CHATS = {
    -1001234567890,  # Group chat ID
}

# Channel/Chat -> Admin mapping (for single-user operation)
CHANNEL_ADMIN_MAP = {
    -1001234567890: 123456789,  # Channel/Chat ID -> Admin user_id
}
```

### Session Behavior

1. **Private Chat**: User sends message → Single ongoing session (`private_{user_id}`)

2. **Channel Discussions**: 
   - Channel creates post → New session (`channel_discussion_{chat_id}_post_{post_id}`)
   - User comments on post → Same session as original post
   - Bot always replies to the original post (not to comments)
   - Each original post = separate conversation thread
   - **Uses admin's user_id for all interactions** (single-user mode)

3. **Group Chat**: User sends message → User-specific session (`chat_{chat_id}_{user_id}`)

### Commands

- `/start` - Initialize bot in any context
- `/clear` - Clear current session history
- `/clear_memory` - Clear all memories for user