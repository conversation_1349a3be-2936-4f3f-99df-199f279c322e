# Telegram MCP Client

A Telegram bot with Claude AI integration for processing user requests via MCP (Model Control Protocol).

## Features

- Claude AI integration via the Agno library
- Response streaming
- Smart splitting of long messages for Telegram delivery
- Tool handling with visual representation of execution status
- Asynchronous message processing

## Installation

### Option 1: Docker (Recommended for Easy Deployment)

1. Clone the repository
2. Set up environment: `make setup` (or `cp docker.env.example .env`)
3. Edit `.env` with your API keys
4. Start the application: `make up` (or `docker compose up -d`)

See [DOCKER.md](DOCKER.md) for detailed Docker instructions and [DEPLOYMENT.md](DEPLOYMENT.md) for auto-deployment setup.

### Option 2: Local Python Installation

1. Clone the repository
2. Create a virtual environment: `python -m venv .venv`
3. Activate the environment:
    - Windows: `.venv\Scripts\activate`
    - Linux/Mac: `source .venv/bin/activate`
4. Install dependencies: `pip install -r requirements.txt`
5. Create a `.env` file from `.env.example` and fill it

## Running

### Docker
```bash
# Production
make up

# Development with live reload
make dev

# View logs
make logs
```

### Local Python
```bash
python main.py
```

## 🚀 Auto-Deployment

The project includes GitHub Actions for automatic deployment to VPS:

```bash
# Setup auto-deploy (one time)
# 1. Create SSH key on VPS
# 2. Add GitHub secrets (VPS_HOST, VPS_USER, VPS_SSH_KEY)
# 3. Push to main branch = auto deploy!

# Manual deployment commands
make update              # Update with minimal downtime
make update-with-downtime # Update with full restart
```

See [DEPLOYMENT.md](DEPLOYMENT.md) for detailed setup instructions.

## Configuration

Main settings are located in the `src/config.py` file, including:

- `MAX_HISTORY_LENGTH` - maximum number of messages in history
- `TOOL_CALL_TIMEOUT` - timeout for tool calls (in seconds)
- `MAX_MESSAGE_LENGTH` - maximum message length (can be reduced for testing)
- `CLAUDE_MODEL` - Claude model being used

## Detailed Documentation

A more detailed description of the project's architecture and components is available in
the [ARCHITECTURE.md](ARCHITECTURE.md) file.
