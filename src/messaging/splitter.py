import logging
from typing import List
from src.utils.config import MAX_MESSAGE_LENGTH

logger = logging.getLogger(__name__)

class MessageSplitter:
    """Handles splitting long messages into multiple parts for Telegram."""

    @staticmethod
    def split_message(text: str, max_length: int = MAX_MESSAGE_LENGTH) -> List[str]:
        """
        Split a long message into multiple parts that fit within Telegram's message length limit.
        
        Args:
            text: The message text to split
            max_length: Maximum length of each message part (defaults to Telegram's limit)
            
        Returns:
            List of message parts
        """
        if not text:
            return []
            
        if len(text) <= max_length:
            return [text]
            
        parts = []
        remaining_text = text
        
        while remaining_text:
            # Find a good split point
            split_index = MessageSplitter._find_split_point(remaining_text, max_length)
            
            # Extract the current part
            current_part = remaining_text[:split_index]
            remaining_text = remaining_text[split_index:].lstrip()
            
            # Add part to the list
            parts.append(current_part)
            
        return parts
    
    @staticmethod
    def _find_split_point(text: str, max_length: int) -> int:
        """
        Find an optimal point to split the text, preferring paragraph breaks,
        then sentence breaks, then word breaks.
        
        Args:
            text: Text to split
            max_length: Maximum length to consider
            
        Returns:
            Index where the text should be split
        """
        if len(text) <= max_length:
            return len(text)
            
        # Try to split at paragraph break
        for i in range(max_length, 0, -1):
            if i < len(text) and text[i-2:i] == "\n\n":
                return i
                
        # Try to split at sentence break
        for i in range(max_length, 0, -1):
            if i < len(text) and text[i-1] in ".!?" and (i == len(text) or text[i].isspace()):
                return i
                
        # Try to split at word break
        for i in range(max_length, 0, -1):
            if i < len(text) and text[i-1].isspace():
                return i
                
        # If no good break point found, just split at max_length
        return max_length
