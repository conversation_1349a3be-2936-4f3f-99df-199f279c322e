import time
import logging
from typing import Op<PERSON>, List, Union
from src.messaging.telegram_adapter import TelegramAdapter

logger = logging.getLogger(__name__)


class MessageProcessor:
    """Handles message sending and updates during query processing."""

    def __init__(self, context, chat_id: int, reply_to_message_id: Optional[int] = None):
        self.context = context
        self.chat_id = chat_id
        self.reply_to_message_id = reply_to_message_id
        self.message_id = None
        self.message_ids: List[int] = []  # For tracking multiple message parts
        self.last_update_time = 0

    async def send_initial_message(self, text: str) -> Optional[int]:
        """Send the initial message and return its ID."""
        result = await TelegramAdapter.send_or_edit_message(
            self.context, self.chat_id, None, text, self.reply_to_message_id
        )

        # Handle both single message ID and list of message IDs
        if isinstance(result, list):
            if result:  # If we have at least one message ID
                self.message_ids = result
                self.message_id = result[0]  # Use the first message as primary
                logger.debug(
                    f"Initial message sent as {len(result)} parts. Primary ID: {self.message_id}")
            else:
                logger.warning(
                    "No message IDs returned when sending initial message")
                self.message_id = None
        else:
            self.message_id = result
            self.message_ids = [result] if result else []
            logger.debug(f"Initial message sent with ID: {self.message_id}")

        return self.message_id

    async def update_message(self, text: str) -> bool:
        """Update the existing message with new content.

        If the message is too long, it will be split into multiple messages.
        Only the first message will be edited, and additional parts will be sent as new messages.
        """
        if not self.message_id:
            logger.warning("Tried to update message, but no message_id")
            return False

        # Always use the first message as the primary for updates
        result = await TelegramAdapter.send_or_edit_message(
            self.context, self.chat_id, self.message_id, text
        )

        # Handle both single message ID and list of message IDs
        if isinstance(result, list):
            if result:
                # Update the list of message IDs, keeping the first as primary
                self.message_ids = result
                self.message_id = result[0]  # Use the first message as primary
                logger.debug(
                    f"Message updated as {len(result)} parts. Primary ID: {self.message_id}")
                self.last_update_time = time.time()
                return True
            else:
                logger.warning("No message IDs returned when updating message")
                return False
        elif result:
            # If we get a single ID, update both the primary and the list
            self.message_id = result
            self.message_ids = [result]
            self.last_update_time = time.time()
            logger.debug(f"Message ID updated: {self.message_id}")
            return True

        return False

    async def send_error_message(self, text: str) -> None:
        """Send or update with an error message."""
        result = await TelegramAdapter.send_or_edit_message(
            self.context, self.chat_id, self.message_id, text
        )

        # Update message tracking if needed
        if isinstance(result, list) and result:
            self.message_ids = result
            self.message_id = result[0]
        elif result:
            self.message_id = result
            self.message_ids = [result]
