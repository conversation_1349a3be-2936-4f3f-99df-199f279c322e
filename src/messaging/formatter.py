from typing import List, Dict, Set

from .components import <PERSON><PERSON><PERSON>po<PERSON>, Text<PERSON><PERSON>ponent, Tool<PERSON><PERSON>ponent, MessageComponentType
from src.ai.types import ToolCall


class MessageFormatter:
    """Manages message components and handles formatting"""

    def __init__(self):
        self.components: List[MessageComponent] = []
        self.tool_calls: Dict[str, ToolCall] = {}
        self.processed_tool_calls: Set[str] = set()

    def add_text(self, text: str) -> None:
        """Add text content to the message"""
        if text and text.strip():
            self.components.append(TextComponent(text))

    def add_tool(self, tool_name: str, tool_call_id: str, finished: bool = False, is_error: bool = False) -> None:
        """Add a tool component to the message if it doesn't already exist"""

        tool_call = ToolCall(tool_name, tool_call_id, finished, is_error)
        key = tool_call.get_key()

        if key in self.processed_tool_calls:
            return

        self.processed_tool_calls.add(key)

        # Check if we already have a component for this tool_call_id
        if tool_call_id in self.tool_calls:
            existing_tool = self.tool_calls[tool_call_id]

            # If component exists, update it
            if existing_tool.component_index is not None:
                component = self.components[existing_tool.component_index]

                if isinstance(component, ToolComponent):
                    component.update_status(finished, is_error)

                    existing_tool.status = finished
                    existing_tool.is_error = is_error

                    return

        # If no existing component or couldn't update, create new one
        tool_component = ToolComponent(tool_name, tool_call_id, finished, is_error)
        self.components.append(tool_component)

        # Track the new tool call
        tool_call.component_index = len(self.components) - 1
        self.tool_calls[tool_call_id] = tool_call

    def update_tool(self, tool_call_id: str, finished: bool, is_error: bool = False) -> bool:
        """Update an existing tool's status or add a new one if it doesn't exist"""

        key = f"{tool_call_id}:{finished}"

        if key in self.processed_tool_calls:
            return True

        self.processed_tool_calls.add(key)

        # Check if we have the tool
        if tool_call_id in self.tool_calls:
            existing_tool = self.tool_calls[tool_call_id]

            # Only update if the status is changing
            if existing_tool.finished != finished or existing_tool.is_error != is_error:
                if existing_tool.component_index is not None:
                    component = self.components[existing_tool.component_index]
                    if isinstance(component, ToolComponent):
                        component.update_status(finished, is_error)

                        existing_tool.status = finished
                        existing_tool.is_error = is_error

                        return True

        # If tool doesn't exist or couldn't update, create a new one
        self.add_tool(tool_call_id.split(":")[-1], tool_call_id, finished, is_error)

        return True

    def format_message(self) -> str:
        """Format the complete message with proper spacing between components"""
        if not self.components:
            return ""

        formatted_message = ""
        prev_component = None

        for component in self.components:
            # Skip empty components
            if not component.get_content().strip():
                continue

            # Handle spacing based on component types
            if prev_component:

                # Double newline between text and tool
                if prev_component.component_type == MessageComponentType.TEXT and component.component_type == MessageComponentType.TOOL:
                    formatted_message = formatted_message.rstrip("\n") + "\n\n"

                # Single newline between tools
                elif prev_component.component_type == MessageComponentType.TOOL and component.component_type == MessageComponentType.TOOL:
                    formatted_message = formatted_message.rstrip("\n") + "\n"

                # Double newline between tool and text
                elif prev_component.component_type == MessageComponentType.TOOL and component.component_type == MessageComponentType.TEXT:
                    formatted_message = formatted_message.rstrip("\n") + "\n\n"

            # Add component content
            formatted_message += component.get_content()
            prev_component = component

        # Ensure message doesn't end with hanging newlines
        return formatted_message.rstrip("\n")
