from telegram.ext import ContextTypes
from telegram.error import BadR<PERSON><PERSON>
from telegram import constants

import logging
import re
from typing import List

from src.utils.config import MAX_MESSAGE_LENGTH
from src.messaging.splitter import MessageSplitter

logger = logging.getLogger(__name__)


def sanitize_html(text: str) -> str:
    """Sanitize text for Telegram HTML by escaping only unmatched special characters."""
    if not text:
        return text
    
    # Only escape & that are not part of HTML entities
    import re
    
    # First escape standalone & characters (not part of &amp;, &lt;, &gt;)
    text = re.sub(r'&(?!(?:amp|lt|gt);)', '&amp;', text)
    
    # Don't escape < and > that are part of valid HTML tags
    # Only escape < and > that are not part of supported tags
    supported_tags = ['b', 'strong', 'i', 'em', 'u', 'ins', 's', 'del', 'strike', 
                     'code', 'pre', 'a', 'blockquote', 'tg-spoiler']
    
    # Create pattern for valid tags (opening and closing)
    tag_pattern = r'</?(?:' + '|'.join(supported_tags) + r')(?:\s[^>]*)?>'
    
    # Split text by valid tags to process only text between tags
    parts = re.split(f'({tag_pattern})', text)
    
    result = ""
    for part in parts:
        if re.match(tag_pattern, part):
            # This is a valid HTML tag, keep as is
            result += part
        else:
            # This is text content, escape < and >
            part = part.replace('<', '&lt;').replace('>', '&gt;')
            result += part
    
    return result


class TelegramAdapter:
    """Handles sending and updating messages via Telegram."""

    @staticmethod
    async def send_or_edit_message(
            context: ContextTypes.DEFAULT_TYPE,
            chat_id: int,
            message_id: int | None,
            text: str,
            reply_to_message_id: int | None = None,
    ) -> int | List[int]:
        """Send a new message or edit an existing one.

        If the message is too long, it will be split into multiple messages.
        Returns either a single message ID or a list of message IDs if split.
        """
        if not text and message_id is not None:
            logger.debug(
                f"Chat {chat_id}: Skipping edit for message {message_id} with empty text.")
            return message_id

        # Sanitize text for HTML
        text = sanitize_html(text)
        
        # Check if message needs to be split
        if len(text) > MAX_MESSAGE_LENGTH:
            logger.info(
                f"Chat {chat_id}: Message length {len(text)} exceeds limit {MAX_MESSAGE_LENGTH}. Splitting into multiple messages.")
            return await TelegramAdapter._handle_split_message(context, chat_id, message_id, text, reply_to_message_id)

        # Handle single message case
        try:
            if message_id is None:
                logger.debug(f"Chat {chat_id}: Sending new message.")

                message = await context.bot.send_message(
                    chat_id=chat_id,
                    text=text,
                    reply_to_message_id=reply_to_message_id,
                    parse_mode=constants.ParseMode.HTML
                )
                logger.info(
                    f"Chat {chat_id}: Sent new message {message.message_id}")

                return message.message_id
            else:
                logger.debug(f"Chat {chat_id}: Editing message {message_id}.")

                await context.bot.edit_message_text(
                    chat_id=chat_id,
                    message_id=message_id,
                    text=text,
                    parse_mode=constants.ParseMode.HTML
                )

                logger.debug(
                    f"Chat {chat_id}: Edited message {message_id} successfully.")

                return message_id
        except BadRequest as e:
            error_message = str(e).lower()

            if "message is not modified" in error_message:
                logger.debug(
                    f"Chat {chat_id}: Message {message_id} was not modified.")

                return message_id
            else:
                logger.warning(
                    f"Chat {chat_id}: Failed to {'edit' if message_id else 'send'} message {message_id} due to BadRequest: {e}")

                return message_id

        except Exception as e:
            logger.error(
                f"Chat {chat_id}: Unexpected error {'editing' if message_id else 'sending'} message {message_id}: {e}",
                exc_info=True)
        return None if message_id is None else message_id

    @staticmethod
    async def _handle_split_message(
            context: ContextTypes.DEFAULT_TYPE,
            chat_id: int,
            message_id: int | None,
            text: str,
            reply_to_message_id: int | None = None,
    ) -> List[int]:
        """Handle splitting a long message into multiple parts.

        Args:
            context: The telegram context
            chat_id: The chat ID
            message_id: The message ID to edit (if any)
            text: The full text to send

        Returns:
            List of message IDs for all parts
        """
        # Split the message into parts
        message_parts = MessageSplitter.split_message(text)
        
        # Sanitize each part for HTML
        message_parts = [sanitize_html(part) for part in message_parts]
        message_ids = []

        if not message_parts:
            logger.warning(
                f"Chat {chat_id}: No message parts generated from text of length {len(text)}")
            return [message_id] if message_id else []

        try:
            # If there is an existing message, use it for the first part
            if message_id is not None:
                try:
                    logger.debug(
                        f"Chat {chat_id}: Editing message {message_id} with first part.")

                    # Try to edit the message
                    await context.bot.edit_message_text(
                        chat_id=chat_id,
                        message_id=message_id,
                        text=message_parts[0],
                        parse_mode=constants.ParseMode.HTML
                    )

                    message_ids.append(message_id)
                    start_index = 1  # Start sending new messages from the second part
                except BadRequest as e:
                    if "Message is not modified" in str(e):
                        # If the message was not modified (same content), just add the ID and continue
                        logger.debug(
                            f"Chat {chat_id}: Message {message_id} was not modified (same content).")
                        message_ids.append(message_id)
                        start_index = 1
                    elif "Can't parse entities" in str(e):
                        # Entity parsing error, try sending as a new message
                        logger.warning(
                            f"Chat {chat_id}: Entity parsing error for message {message_id}: {e}")
                        start_index = 0
                    else:
                        # If another error, start from the first part
                        logger.warning(
                            f"Chat {chat_id}: Could not edit message {message_id}: {e}")
                        start_index = 0
                except Exception as e:
                    logger.warning(
                        f"Chat {chat_id}: Error editing message {message_id}: {e}")
                    start_index = 0
            else:
                start_index = 0  # Start from the first part if no existing message

            # Send the remaining parts as new messages
            for i in range(start_index, len(message_parts)):
                try:
                    # Only use reply_to_message_id for the first message when no existing message to edit
                    reply_to = reply_to_message_id if (message_id is None and i == 0) else None
                    
                    message = await context.bot.send_message(
                        chat_id=chat_id,
                        text=message_parts[i],
                        reply_to_message_id=reply_to,
                        parse_mode=constants.ParseMode.HTML
                    )

                    message_ids.append(message.message_id)
                    logger.info(
                        f"Chat {chat_id}: Sent message part {i+1}/{len(message_parts)} with ID {message.message_id}")
                except Exception as e:
                    logger.error(
                        f"Chat {chat_id}: Failed to send message part {i+1}: {e}")

            return message_ids

        except Exception as e:
            logger.error(
                f"Chat {chat_id}: Error handling split message: {e}",
                exc_info=True
            )

            # Return collected message IDs
            return message_ids if message_ids else ([message_id] if message_id else [])
