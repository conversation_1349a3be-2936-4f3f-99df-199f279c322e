import sqlite3
import logging
from typing import Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class DatabaseManager:
    def __init__(self, db_path: str = ".tmp/agent.db"):
        self.db_path = db_path
    
    def get_connection(self) -> sqlite3.Connection:
        """Get database connection with proper settings."""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def table_exists(self, table_name: str) -> bool:
        """Check if a table exists in the database."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name=?
            """, (table_name,))
            return cursor.fetchone() is not None
    
    def run_migrations(self):
        """Run database migrations for reminder system."""
        logger.info("Checking and running database migrations...")

        with self.get_connection() as conn:
            cursor = conn.cursor()

            # Check if reminders table exists and has correct structure
            if not self.table_exists("reminders"):
                logger.info("Creating reminders table...")
                cursor.execute("""
                    CREATE TABLE reminders (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        message_text TEXT NOT NULL,
                        scheduled_time_utc TEXT NOT NULL,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        is_sent INTEGER DEFAULT 0
                    )
                """)

                # Create indexes for better performance
                cursor.execute("CREATE INDEX idx_reminders_user_id ON reminders(user_id)")
                cursor.execute("CREATE INDEX idx_reminders_scheduled_time ON reminders(scheduled_time_utc)")
                cursor.execute("CREATE INDEX idx_reminders_status ON reminders(is_sent)")

            conn.commit()
            logger.info("Database migrations completed successfully")


# Global database manager instance
db_manager = DatabaseManager()