import logging
from telegram import Update
from src.utils.config import WHITELIST_USERS, WHITELIST_CHANNELS, WHITELIST_CHANNEL_CHATS

logger = logging.getLogger(__name__)


def is_authorized(update: Update) -> bool:
    """
    Check if the update is from an authorized user/chat/channel.
    
    Args:
        update: Telegram update object
        
    Returns:
        True if authorized, False otherwise
    """
    if not update.effective_chat or not update.effective_user:
        return False
    
    chat_id = update.effective_chat.id
    user_id = update.effective_user.id
    chat_type = update.effective_chat.type
    
    # Special case: automatic forwards from channels (user_id = 777000)
    if (user_id == 777000 and 
        update.message and 
        update.message.is_automatic_forward and 
        update.message.forward_origin and 
        hasattr(update.message.forward_origin, 'chat')):
        # For automatic forwards, check the source channel and destination chat
        source_channel_id = update.message.forward_origin.chat.id
        logger.info(f"Checking authorization for automatic forward from channel {source_channel_id} to chat {chat_id}")
        
        # Check if source channel is whitelisted
        if source_channel_id in WHITELIST_CHANNELS:
            logger.info(f"Source channel {source_channel_id} is whitelisted")
            # Also check if destination chat is whitelisted
            if chat_id in WHITELIST_CHANNEL_CHATS:
                logger.info(f"Destination chat {chat_id} is whitelisted")
                return True
        
        logger.warning(f"Automatic forward not authorized - Source: {source_channel_id}, Dest: {chat_id}")
        return False
    
    # Check user whitelist (always check for any type of chat)
    if user_id in WHITELIST_USERS:
        logger.info(f"User {user_id} is whitelisted")
        return True
    
    # Check chat-specific whitelists
    if chat_type == "private":
        # For private chats, only user whitelist matters
        return False
    elif chat_type == "channel":
        if chat_id in WHITELIST_CHANNELS:
            logger.info(f"Channel {chat_id} is whitelisted")
            return True
    elif chat_type in ["group", "supergroup"]:
        if chat_id in WHITELIST_CHANNEL_CHATS:
            logger.info(f"Chat {chat_id} is whitelisted")
            return True
    
    logger.warning(f"Unauthorized access attempt - User: {user_id}, Chat: {chat_id} ({chat_type})")
    logger.debug(f"  WHITELIST_USERS: {WHITELIST_USERS}")
    logger.debug(f"  WHITELIST_CHANNELS: {WHITELIST_CHANNELS}")  
    logger.debug(f"  WHITELIST_CHANNEL_CHATS: {WHITELIST_CHANNEL_CHATS}")
    return False


def get_authorization_info(update: Update) -> str:
    """Get info string for authorization logging."""
    if not update.effective_chat or not update.effective_user:
        return "Unknown"
    
    chat_id = update.effective_chat.id
    user_id = update.effective_user.id
    chat_type = update.effective_chat.type
    username = update.effective_user.username or "No username"
    
    return f"User: {user_id} (@{username}), Chat: {chat_id} ({chat_type})"