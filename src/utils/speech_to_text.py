import logging
import io
from typing import Optional

from deepgram import Deepgram<PERSON>lient, PrerecordedOptions

from src.utils.config import DEEPGRAM_API_KEY

logger = logging.getLogger(__name__)


class SpeechToTextService:
    """Service for converting speech to text using Deepgram."""

    def __init__(self):
        if not DEEPGRAM_API_KEY:
            logger.error("DEEPGRAM_API_KEY is not set. Speech-to-text functionality will not work.")
            self.client = None
        else:
            # Initialize the Deepgram client with API key
            self.client = DeepgramClient(DEEPGRAM_API_KEY)

    async def transcribe_audio(self, audio_bytes: bytes, language: str = "ru-RU") -> Optional[str]:
        """
        Transcribe audio bytes to text.
        
        Args:
            audio_bytes: The audio content as bytes
            language: The language code (default: "ru-RU" for Russian)
            
        Returns:
            The transcribed text or None if transcription failed
        """
        if not self.client:
            logger.error("Deepgram client not initialized. Cannot transcribe audio.")
            return None

        try:
            # Set up transcription options
            options = PrerecordedOptions(
                model="nova-2",
                smart_format=True,
                language=language,
                detect_language=True,
            )
            
            # Prepare audio payload with mimetype
            source = {
                "buffer": audio_bytes,
                "mimetype": "audio/ogg"  # Adjust based on your actual audio format
            }
            
            # Using synchronous version without await
            response = self.client.listen.prerecorded.v("1").transcribe_file(
                source, options
            )
            
            # Extract transcript from the response
            if response and response.results:
                if response.results.channels and len(response.results.channels) > 0:
                    alternatives = response.results.channels[0].alternatives
                    if alternatives and len(alternatives) > 0:
                        transcript = alternatives[0].transcript
                        
                        if transcript:
                            logger.info(f"Transcription successful. Length: {len(transcript)} chars")
                            return transcript
            
            logger.warning("No transcript found in response")
            return None
                
        except Exception as e:
            logger.error(f"Error transcribing audio: {e}", exc_info=True)
            return None


# Create a singleton instance
speech_to_text_service = SpeechToTextService()