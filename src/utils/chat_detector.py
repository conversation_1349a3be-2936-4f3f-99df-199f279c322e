import logging
from telegram import Update

logger = logging.getLogger(__name__)


def is_channel_discussion_group(update: Update) -> bool:
    """
    Determine if this is a discussion group linked to a channel.
    
    In Telegram, when a channel has comments enabled, messages are sent to a linked
    discussion group. We can detect this by checking message properties.
    """
    if not update.message:
        return False
    
    # Check if the message is from a channel (forwarded from channel)
    if (update.message.forward_origin and 
        hasattr(update.message.forward_origin, 'chat') and 
        update.message.forward_origin.chat.type == "channel"):
        logger.debug(f"Detected channel discussion: forwarded from channel {update.message.forward_origin.chat.id}")
        return True
    
    # Check if this is a reply to a message that was forwarded from a channel
    if (update.message.reply_to_message and 
        update.message.reply_to_message.forward_origin and 
        hasattr(update.message.reply_to_message.forward_origin, 'chat') and 
        update.message.reply_to_message.forward_origin.chat.type == "channel"):
        logger.debug(f"Detected channel discussion: reply to channel message from {update.message.reply_to_message.forward_origin.chat.id}")
        return True
    
    # Additional check: if the group has a linked channel (this requires bot admin access)
    # We can't easily detect this without making API calls, so we'll use the above methods
    
    return False


def get_original_channel_info(update: Update) -> tuple[int, int]:
    """
    Get the original channel ID and post ID from a channel discussion message.
    
    Returns:
        tuple: (channel_id, post_id) or (0, 0) if not found
    """
    if not update.message:
        return (0, 0)
    
    # Direct channel post
    if (update.message.forward_origin and 
        hasattr(update.message.forward_origin, 'chat') and 
        update.message.forward_origin.chat.type == "channel"):
        channel_id = update.message.forward_origin.chat.id
        post_id = getattr(update.message.forward_origin, 'message_id', update.message.message_id)
        return (channel_id, post_id)
    
    # Reply to channel post
    if (update.message.reply_to_message and 
        update.message.reply_to_message.forward_origin and 
        hasattr(update.message.reply_to_message.forward_origin, 'chat') and 
        update.message.reply_to_message.forward_origin.chat.type == "channel"):
        channel_id = update.message.reply_to_message.forward_origin.chat.id
        post_id = getattr(update.message.reply_to_message.forward_origin, 'message_id', update.message.reply_to_message.message_id)
        return (channel_id, post_id)
    
    return (0, 0)