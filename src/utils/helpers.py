from typing import Optional
import logging
from telegram import constants
from telegram.ext import ContextTypes
from telegram.error import BadRequest

logger = logging.getLogger(__name__)


def escape_html(text: str, max_length: int = 4000) -> str:
    """Escape HTML special characters and truncate if needed."""
    if not isinstance(text, str):
        text = str(text)

    # Properly escape HTML special characters
    escaped = text.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

    # Truncate if too long
    if len(escaped) > max_length:
        return escaped[:max_length] + "..."
    return escaped


async def send_telegram_message(
        context: ContextTypes.DEFAULT_TYPE,
        chat_id: int,
        text: str,
        message_to_edit: Optional = None
) -> Optional:
    """Send or edit a Telegram message with proper error handling."""
    max_length = constants.MessageLimit.MAX_TEXT_LENGTH
    sent_message = None

    try:
        # Truncate if needed
        if len(text) > max_length:
            text = text[:max_length - 4] + "..."

        # Edit existing message or send new one
        if message_to_edit:
            try:
                sent_message = await message_to_edit.edit_text(text, parse_mode=constants.ParseMode.HTML)
            except BadRequest as e:
                if "message is not modified" in str(e):
                    # Message is identical, just return the original
                    sent_message = message_to_edit
                elif "message to edit not found" in str(e):
                    # Message can't be edited, send a new one
                    sent_message = await context.bot.send_message(chat_id, text, parse_mode=constants.ParseMode.HTML)
                else:
                    # Other BadRequest error, re-raise
                    raise
        else:
            # Send new message
            sent_message = await context.bot.send_message(chat_id, text, parse_mode=constants.ParseMode.HTML)

    except BadRequest:
        # Try without HTML formatting if that failed
        try:
            # Strip HTML tags
            plain_text = text.replace('<pre>', '').replace('</pre>', '').replace('<code>', '').replace('</code>', '') \
                .replace('<b>', '').replace('</b>', '').replace('<i>', '').replace('</i>', '')

            if len(plain_text) > max_length:
                plain_text = plain_text[:max_length - 4] + "..."

            if message_to_edit:
                sent_message = await message_to_edit.edit_text(plain_text)
            else:
                sent_message = await context.bot.send_message(chat_id, plain_text)
        except Exception as e:
            logger.error(f"Failed to send message even without formatting: {e}")
            # If everything fails, try a very simple message
            if not message_to_edit:
                try:
                    await context.bot.send_message(chat_id, "Error displaying message")
                except:
                    pass
    except Exception as e:
        logger.error(f"Unexpected error sending message: {e}", exc_info=True)

    return sent_message
