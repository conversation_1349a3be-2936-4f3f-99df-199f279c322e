from typing import Dict, Optional
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class SessionType(Enum):
    PRIVATE = "private"
    CHANNEL = "channel"
    CHAT = "chat"


@dataclass
class SessionContext:
    session_id: str
    session_type: SessionType
    user_id: int
    chat_id: int
    thread_id: Optional[int] = None  # For channel posts or forum topics
    chat_title: Optional[str] = None  # For reminder context


class SessionManager:
    def __init__(self):
        self._sessions: Dict[str, SessionContext] = {}
    
    def get_session_id(self, chat_type: str, chat_id: int, user_id: int, message_id: Optional[int] = None, reply_to_message_id: Optional[int] = None, is_channel_discussion: bool = False, is_channel_post: bool = False) -> str:
        """
        Generate session ID based on chat context.
        
        For private chats: private_{user_id}
        For channels: channel_{chat_id}_post_{post_id}
        For channel discussions (comments): channel_{original_channel_id}_post_{original_post_id}
        For group chats: chat_{chat_id}_{user_id}
        """
        if chat_type == "private":
            return f"private_{user_id}"
        elif chat_type == "channel":
            # True channel posts (not discussion groups)
            if reply_to_message_id:
                # Reply to a channel post
                return f"channel_{chat_id}_post_{reply_to_message_id}"
            else:
                # New channel post
                return f"channel_{chat_id}_post_{message_id}"
        elif chat_type in ["group", "supergroup"]:
            if is_channel_discussion:
                # This is a discussion group linked to a channel
                if reply_to_message_id:
                    # Comment on a channel post - all comments/replies share the same session
                    # No user_id in session - everyone shares the same conversation
                    return f"channel_discussion_{chat_id}_post_{reply_to_message_id}"
                else:
                    # New message in discussion group
                    if is_channel_post:
                        # This is a new channel post - creates new session
                        return f"channel_discussion_{chat_id}_post_{message_id}"
                    else:
                        # This is a user message not replying to anything - treat as regular chat
                        return f"chat_{chat_id}_{user_id}"
            else:
                # Regular group chat - one session per user
                return f"chat_{chat_id}_{user_id}"
        else:
            # Fallback
            return f"unknown_{chat_id}_{user_id}"
    
    def get_session_context(self, chat_type: str, chat_id: int, user_id: int, message_id: Optional[int] = None, reply_to_message_id: Optional[int] = None, is_channel_discussion: bool = False, is_channel_post: bool = False, chat_title: Optional[str] = None) -> SessionContext:
        """Get or create session context."""
        session_id = self.get_session_id(chat_type, chat_id, user_id, message_id, reply_to_message_id, is_channel_discussion, is_channel_post)
        
        if session_id not in self._sessions:
            # Determine session type
            if chat_type == "private":
                session_type = SessionType.PRIVATE
            elif chat_type == "channel" or is_channel_discussion:
                session_type = SessionType.CHANNEL
            else:
                session_type = SessionType.CHAT
            
            # For channels/discussions, use the original post message ID as thread_id
            if chat_type == "channel" or is_channel_discussion:
                if reply_to_message_id:
                    # This is a comment - thread_id is the original post
                    thread_id = reply_to_message_id
                else:
                    # This is a new post - thread_id is this message
                    thread_id = message_id
            else:
                thread_id = None
            
            # For channel discussions, we want to preserve the original session's user_id (channel)
            # but track who actually sent this specific message
            if is_channel_discussion and reply_to_message_id:
                # This is a comment on a channel post - use channel ID as session owner
                # but the actual user_id is tracked separately in the session context
                session_user_id = user_id  # Keep track of who is actually writing
            else:
                session_user_id = user_id
            
            self._sessions[session_id] = SessionContext(
                session_id=session_id,
                session_type=session_type,
                user_id=session_user_id,
                chat_id=chat_id,
                thread_id=thread_id,
                chat_title=chat_title
            )
            
            logger.info(f"Created new session: {session_id} (type: {session_type.value}, chat_id: {chat_id}, user_id: {user_id}, thread_id: {thread_id})")
        
        return self._sessions[session_id]
    
    def clear_session(self, session_id: str) -> bool:
        """Clear specific session."""
        if session_id in self._sessions:
            del self._sessions[session_id]
            logger.info(f"Cleared session: {session_id}")
            return True
        return False
    
    def get_all_sessions(self) -> Dict[str, SessionContext]:
        """Get all active sessions."""
        return self._sessions.copy()


# Global session manager instance
session_manager = SessionManager()