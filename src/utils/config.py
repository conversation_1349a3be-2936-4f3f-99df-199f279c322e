import os
from dotenv import load_dotenv
from telegram.constants import MessageLimit
from typing import Set

load_dotenv()

MAX_HISTORY_LENGTH = 20  # Limit message history
TOOL_CALL_TIMEOUT = 60.0  # Timeout for tool calls (seconds)
MCP_CONNECT_TIMEOUT = 30.0  # Timeout for MCP server connection (seconds)
MAX_TOOL_CALLS_PER_TURN = 30  # Maximum tool calls per message

MAX_MESSAGE_LENGTH = MessageLimit.MAX_TEXT_LENGTH

TELEGRAM_TOKEN = os.getenv("TELEGRAM_TOKEN")
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")
CLAUDE_MODEL = os.getenv("CLAUDE_MODEL", "claude-3-5-sonnet-20240620")
CLAUDE_SYSTEM_PROMPT = os.getenv("CLAUDE_SYSTEM_PROMPT")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
DEEPGRAM_API_KEY = os.getenv("DEEPGRAM_API_KEY")

# Whitelist configuration
WHITELIST_USERS: Set[int] = {
    1730029726,  # Stanislav
    523310512, # Annete
}

WHITELIST_CHANNELS: Set[int] = {
    -1002504312241 # Assistant Channel Chat
}

WHITELIST_CHANNEL_CHATS: Set[int] = {
    -1002279419343 # Assistant Channel
}

# Channel/Chat -> Admin mapping (for single-user channel discussions)
# Maps channel_id or chat_id to the admin's user_id
CHANNEL_ADMIN_MAP = {
    -1002504312241: 1730029726,  # Assistant Channel Chat -> Your user_id
    -1002279419343: 1730029726,  # Assistant Channel -> Your user_id
}
