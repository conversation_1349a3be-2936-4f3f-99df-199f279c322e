import asyncio
import logging
from datetime import datetime
from typing import Optional, Callable
import pytz
from src.utils.database import db_manager

logger = logging.getLogger(__name__)

# Global callback for sending messages - set by bot
_message_callback: Optional[Callable] = None

# Global scheduler instance
_scheduler_task: Optional[asyncio.Task] = None
_running = False


def set_message_callback(callback: Callable):
    """Set the callback function for sending messages."""
    global _message_callback
    _message_callback = callback
    logger.info("✅ Message callback set for reminder delivery")


async def send_reminder(reminder_id: int, user_id: int, message_text: str):
    """Send a reminder message."""
    try:
        logger.info(f"🔔 Sending reminder {reminder_id} to user {user_id}")
        
        if _message_callback:
            await _message_callback(user_id=user_id, message=message_text)
            
            # Mark as sent in database
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("UPDATE reminders SET is_sent = 1 WHERE id = ?", (reminder_id,))
                conn.commit()
                
            logger.info(f"✅ Reminder {reminder_id} sent and marked as delivered")
        else:
            logger.warning("❌ No message callback set for reminder delivery")
            
    except Exception as e:
        logger.error(f"❌ Failed to send reminder {reminder_id}: {e}")


async def scheduler_loop():
    """Main scheduler loop that checks for due reminders."""
    global _running
    _running = True
    logger.info("🚀 Simple scheduler started")
    
    while _running:
        try:
            current_time = datetime.now(pytz.UTC)
            
            # Get all pending reminders that are due
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT id, user_id, message_text, scheduled_time_utc 
                    FROM reminders 
                    WHERE is_sent = 0 AND scheduled_time_utc <= ?
                    ORDER BY scheduled_time_utc ASC
                """, (current_time.isoformat(),))
                
                due_reminders = cursor.fetchall()
            
            # Send due reminders
            for reminder in due_reminders:
                reminder_id = reminder['id']
                user_id = reminder['user_id']
                message_text = reminder['message_text']
                scheduled_time = reminder['scheduled_time_utc']
                
                logger.info(f"📅 Processing due reminder {reminder_id} (scheduled: {scheduled_time})")
                await send_reminder(reminder_id, user_id, message_text)
            
            # Sleep for 10 seconds before checking again
            await asyncio.sleep(10)
            
        except Exception as e:
            logger.error(f"❌ Error in scheduler loop: {e}")
            await asyncio.sleep(30)  # Wait longer on error


async def start_scheduler():
    """Start the simple scheduler."""
    global _scheduler_task, _running
    
    if _scheduler_task and not _scheduler_task.done():
        logger.warning("⚠️ Scheduler already running")
        return
    
    logger.info("🚀 Starting simple reminder scheduler...")
    _scheduler_task = asyncio.create_task(scheduler_loop())
    
    # Log existing pending reminders
    try:
        current_time = datetime.now(pytz.UTC)
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, scheduled_time_utc, message_text FROM reminders 
                WHERE is_sent = 0
                ORDER BY scheduled_time_utc ASC
            """)
            pending_reminders = cursor.fetchall()
        
        logger.info(f"📊 Found {len(pending_reminders)} pending reminders:")
        for reminder in pending_reminders:
            scheduled_time = datetime.fromisoformat(reminder['scheduled_time_utc'])
            is_due = scheduled_time <= current_time
            status = "DUE NOW" if is_due else "FUTURE"
            logger.info(f"  ID {reminder['id']}: {reminder['scheduled_time_utc']} ({status}) - {reminder['message_text'][:50]}...")
            
    except Exception as e:
        logger.error(f"❌ Error logging pending reminders: {e}")


async def stop_scheduler():
    """Stop the simple scheduler."""
    global _scheduler_task, _running
    
    logger.info("🛑 Stopping simple reminder scheduler...")
    _running = False
    
    if _scheduler_task and not _scheduler_task.done():
        _scheduler_task.cancel()
        try:
            await _scheduler_task
        except asyncio.CancelledError:
            pass
    
    logger.info("✅ Simple scheduler stopped")


def create_reminder(user_id: int, message_text: str, scheduled_time_utc: datetime) -> Optional[int]:
    """Create a new reminder in the database."""
    try:
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO reminders (user_id, message_text, scheduled_time_utc)
                VALUES (?, ?, ?)
            """, (user_id, message_text, scheduled_time_utc.isoformat()))
            conn.commit()
            reminder_id = cursor.lastrowid
            logger.info(f"✅ Created reminder {reminder_id} for {scheduled_time_utc.isoformat()}")
            return reminder_id
    except Exception as e:
        logger.error(f"❌ Error creating reminder: {e}")
        return None


def get_user_reminders(user_id: int, status: str = "pending") -> list:
    """Get user's reminders."""
    try:
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            if status == "pending":
                cursor.execute("""
                    SELECT * FROM reminders 
                    WHERE user_id = ? AND is_sent = 0
                    ORDER BY scheduled_time_utc ASC
                """, (user_id,))
            elif status == "completed":
                cursor.execute("""
                    SELECT * FROM reminders 
                    WHERE user_id = ? AND is_sent = 1
                    ORDER BY scheduled_time_utc DESC
                """, (user_id,))
            else:  # all
                cursor.execute("""
                    SELECT * FROM reminders 
                    WHERE user_id = ?
                    ORDER BY scheduled_time_utc DESC
                """, (user_id,))
            
            return cursor.fetchall()
    except Exception as e:
        logger.error(f"❌ Error getting user reminders: {e}")
        return []


def update_reminder(reminder_id: int, message_text: str = None, scheduled_time_utc: datetime = None) -> bool:
    """Update a reminder's message or time."""
    try:
        updates = []
        values = []

        if message_text is not None:
            updates.append("message_text = ?")
            values.append(message_text)

        if scheduled_time_utc is not None:
            updates.append("scheduled_time_utc = ?")
            values.append(scheduled_time_utc.isoformat())

        if not updates:
            return False

        values.append(reminder_id)

        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            query = f"UPDATE reminders SET {', '.join(updates)} WHERE id = ?"
            cursor.execute(query, values)
            conn.commit()
            logger.info(f"✅ Updated reminder {reminder_id}")
            return cursor.rowcount > 0
    except Exception as e:
        logger.error(f"❌ Error updating reminder {reminder_id}: {e}")
        return False


def delete_reminder(reminder_id: int) -> bool:
    """Delete a reminder."""
    try:
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM reminders WHERE id = ?", (reminder_id,))
            conn.commit()
            logger.info(f"✅ Deleted reminder {reminder_id}")
            return cursor.rowcount > 0
    except Exception as e:
        logger.error(f"❌ Error deleting reminder {reminder_id}: {e}")
        return False
