import logging


def setup_logging():
    """Configure logging for the application."""
    logging.basicConfig(
        format='%(asctime)s - %(levelname)s - %(message)s',
        level=logging.INFO
    )
    logger = logging.getLogger(__name__)

    # Set debug level for relevant modules
    logging.getLogger("__main__").setLevel(logging.DEBUG)

    # Set Telegram library loggers to ERROR level only
    logging.getLogger("telegram").setLevel(logging.ERROR)
    logging.getLogger("telethon").setLevel(logging.ERROR)
    logging.getLogger("pyrogram").setLevel(logging.ERROR)

    # Set HTTP libraries used by Telegram to ERROR level only
    logging.getLogger("httpx").setLevel(logging.ERROR)
    logging.getLogger("urllib3").setLevel(logging.ERROR)
    logging.getLogger("requests").setLevel(logging.ERROR)

    return logger
