from typing import Dict, Type, TypeVar, Literal, cast, Union

from telegram.ext import ContextTypes
from agno.tools.mcp import MultiMCPTools

from src.ai.agent import AIAgent

T = TypeVar('T')

DEPENDENCY_TYPES: Dict[str, Type] = {
    'ai_agent': AIAgent,
    'mcp_tools': MultiMCPTools
}

BotDependencyKey = Literal['ai_agent', 'mcp_tools']


def get_dependency(context: ContextTypes.DEFAULT_TYPE, key: BotDependencyKey) -> T:
    """Get a dependency from context.bot_data with automatic type checking"""

    value = context.bot_data.get(key)
    expected_type = DEPENDENCY_TYPES[key]

    if value is None:
        raise KeyError(f"Dependency '{key}' not found in context")
    if not isinstance(value, expected_type):
        raise TypeError(f"Expected {key} to be {expected_type.__name__}, got {type(value).__name__}")

    return cast(T, value)
