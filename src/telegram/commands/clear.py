from telegram import Update
from telegram.ext import ContextTypes

from agno.tools.mcp import MultiMCPTools
from src.ai.agent import get_basic_agent
from src.telegram.types.context import get_dependency
from src.utils.whitelist import is_authorized
from src.utils.session import session_manager
from src.utils.chat_detector import is_channel_discussion_group


async def clear_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    if not is_authorized(update):
        return

    user = update.effective_user
    chat_id = update.effective_chat.id
    user_id = user.id
    chat_type = update.effective_chat.type
    
    # Get session context
    message_id = update.message.message_id
    reply_to_message_id = update.message.reply_to_message.message_id if update.message.reply_to_message else None
    is_discussion = is_channel_discussion_group(update)
    
    session_context = session_manager.get_session_context(
        chat_type=chat_type,
        chat_id=chat_id,
        user_id=user_id,
        message_id=message_id,
        reply_to_message_id=reply_to_message_id,
        is_channel_discussion=is_discussion
    )
    
    # Get MCP tools from context for creating user-specific agents
    mcp_tools: MultiMCPTools = get_dependency(context, "mcp_tools")
    
    # Create a fresh agent for this session
    ai_agent = get_basic_agent(
        user_id=str(user_id),
        session_id=session_context.session_id,
        mcp_tools=mcp_tools
    )
    
    # Delete the session
    ai_agent.agent.storage.delete_session(session_context.session_id)
    
    # Clear from session manager
    session_manager.clear_session(session_context.session_id)
    
    # Update the agent in bot_data for this session
    context.bot_data[f"agent_{session_context.session_id}"] = ai_agent

    await update.message.reply_html(
        f'🔄 <b>Session cleared!</b>\n\nThis session has been reset. Let\'s start fresh! 🎆'
    )


async def clear_memory_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    if not is_authorized(update):
        return

    user = update.effective_user
    chat_id = update.effective_chat.id
    user_id = user.id
    chat_type = update.effective_chat.type
    
    # Get session context
    message_id = update.message.message_id
    reply_to_message_id = update.message.reply_to_message.message_id if update.message.reply_to_message else None
    is_discussion = is_channel_discussion_group(update)
    
    session_context = session_manager.get_session_context(
        chat_type=chat_type,
        chat_id=chat_id,
        user_id=user_id,
        message_id=message_id,
        reply_to_message_id=reply_to_message_id,
        is_channel_discussion=is_discussion
    )
    
    # Get MCP tools from context for creating user-specific agents
    mcp_tools: MultiMCPTools = get_dependency(context, "mcp_tools")
    
    # Create a fresh agent for this session
    ai_agent = get_basic_agent(
        user_id=str(user_id),
        session_id=session_context.session_id,
        mcp_tools=mcp_tools
    )
    
    # Clear all user memories
    if hasattr(ai_agent.agent, 'memory') and ai_agent.agent.memory:
        # Get all memories for this user and delete them
        user_memories = ai_agent.agent.memory.get_user_memories(user_id=str(user_id))
        for memory in user_memories:
            ai_agent.agent.memory.delete_user_memory(user_id=str(user_id), memory_id=memory.memory_id)
    
    # Also clear the session
    ai_agent.agent.storage.delete_session(session_context.session_id)
    
    # Clear from session manager
    session_manager.clear_session(session_context.session_id)
    
    # Update the agent in bot_data for this session
    context.bot_data[f"agent_{session_context.session_id}"] = ai_agent

    await update.message.reply_html(
        f'🧠 <b>All memories cleared!</b>\n\nI\'ve forgotten everything about our previous conversations. It\'s like we\'re meeting for the first time! 🌱😊'
    )
