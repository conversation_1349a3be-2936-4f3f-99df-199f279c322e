from telegram import Update
from telegram.ext import ContextTypes
from src.utils.whitelist import is_authorized


async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    if not is_authorized(update):
        return

    user = update.effective_user
    chat_type = update.effective_chat.type
    
    if chat_type == "private":
        message = f'Hello, {user.mention_html()}! 👋\n\nI\'m ready to help you with any questions!'
    elif chat_type == "channel":
        message = f'Hello! 👋\n\nI\'m ready to help with questions in this channel. Each post creates a separate conversation session.'
    else:
        message = f'Hello! 👋\n\nI\'m ready to help with questions in this chat.'

    await update.message.reply_html(message)
