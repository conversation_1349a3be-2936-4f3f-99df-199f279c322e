import asyncio
import logging
import io
from typing import List, Optional

from telegram import Update
import telegram
from telegram.ext import ContextTypes
from telegram.constants import ChatAction
from anthropic import Anthropic

from agno.media import Image, File
from agno.tools.mcp import MultiMCPTools
from src.ai.agent import AIAgent, get_basic_agent
from src.telegram.types.context import get_dependency
from src.utils.speech_to_text import speech_to_text_service
from src.utils.whitelist import is_authorized, get_authorization_info
from src.utils.session import session_manager
from src.utils.chat_detector import is_channel_discussion_group, get_original_channel_info
from src.utils.config import CHANNEL_ADMIN_MAP, ANTHROPIC_API_KEY
from src.ai.title_generator import title_generator

logger = logging.getLogger(__name__)


async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Universal message handler for private chats, channels, and groups.
    Supports session-based conversations with whitelist protection.
    """
    if not update.message or not update.effective_chat or not update.effective_user:
        return

    # DEBUG: Log message types received
    msg = update.message
    logger.info(f"📨 Message received - Types: text={bool(msg.text)}, photo={bool(msg.photo)}, "
               f"document={bool(msg.document)}, voice={bool(msg.voice)}, audio={bool(msg.audio)}, "
               f"caption={bool(msg.caption)}")
    
    if msg.document:
        logger.info(f"📄 Document details: file_name={msg.document.file_name}, "
                   f"mime_type={msg.document.mime_type}, size={msg.document.file_size}")
    
    if msg.photo:
        logger.info(f"📸 Photo details: count={len(msg.photo)}, "
                   f"largest_size={msg.photo[-1].file_size if msg.photo else 'N/A'}")

    # Skip system messages, but allow automatic forwards from channels
    user_id = update.effective_user.id
    if user_id == 777000:  # Telegram system messages
        # Allow automatic forwards from channels (these are channel posts forwarded to discussion groups)
        if not (update.message.is_automatic_forward and 
                update.message.forward_origin and 
                hasattr(update.message.forward_origin, 'chat')):
            logger.debug(f"Skipping system message from user {user_id}")
            return
        else:
            logger.debug(f"Processing automatic forward from channel {update.message.forward_origin.chat.id}")

    # Check authorization first
    if not is_authorized(update):
        logger.warning(f"Unauthorized access: {get_authorization_info(update)}")
        return

    chat_id = update.effective_chat.id
    chat_type = update.effective_chat.type
    
    # Detect if this is a channel discussion group first
    is_discussion = is_channel_discussion_group(update)
    
    # DEBUG: Log channel detection
    logger.info(f"🔍 Channel detection: chat_type={chat_type}, is_discussion={is_discussion}, user_id={user_id}")
    
    # For channel discussions, we don't need to track user_id in session
    # All participants in a channel post discussion share the same session
    if (update.effective_user.id == 777000 and 
        update.message.is_automatic_forward and 
        update.message.forward_origin and 
        hasattr(update.message.forward_origin, 'chat')):
        is_channel_post = True
        actual_user_id = update.message.forward_origin.chat.id  # Channel that posted
        logger.info(f"Channel post from {actual_user_id}")
        
        # Handle channel post duplication: edit original post to "1" and duplicate content to comments
        duplicated_message = await handle_channel_post_duplication(update, context)
        if duplicated_message:
            # Process the duplicated message through AI
            await process_duplicated_channel_post(update, context, duplicated_message)
            return
    elif chat_type == "channel" and not is_discussion:
        # Direct channel post (not forwarded to discussion group)
        is_channel_post = False
        actual_user_id = update.effective_user.id
        logger.info(f"🎯 DIRECT CHANNEL POST from {actual_user_id} in channel {chat_id}")
        
        # Handle direct channel post - process immediately
        await handle_direct_channel_post(update, context)
        return
    else:
        is_channel_post = False
        actual_user_id = update.effective_user.id  # User who commented
        logger.info(f"User comment from {actual_user_id}")
    
    # For channel discussions, use admin's user_id instead of actual user_id
    # This makes it single-user: admin controls the bot through channel/chat
    if is_discussion:
        # Get admin user_id from mapping
        admin_user_id = CHANNEL_ADMIN_MAP.get(chat_id)
        if admin_user_id:
            user_id = admin_user_id
            logger.info(f"Using admin user_id {admin_user_id} for channel discussion in chat {chat_id}")
        else:
            user_id = actual_user_id
            logger.warning(f"No admin mapping found for chat {chat_id}, using actual user_id {actual_user_id}")
    else:
        user_id = actual_user_id
    
    # Check if message has content we can process
    has_content = (update.message.text or update.message.photo or 
                  update.message.voice or update.message.audio or
                  update.message.document or
                  update.message.caption)
    if not has_content:
        return
    
    # Get session context
    message_id = update.message.message_id
    reply_to_message_id = update.message.reply_to_message.message_id if update.message.reply_to_message else None
    
    # Extract chat title for reminder context
    chat_title = update.effective_chat.title or update.effective_chat.first_name or f"Chat {chat_id}"
    
    # For channel discussions, we might need to adjust the session logic
    if is_discussion:
        original_channel_id, original_post_id = get_original_channel_info(update)
        logger.info(f"Detected channel discussion: original_channel={original_channel_id}, original_post={original_post_id}")
    
    session_context = session_manager.get_session_context(
        chat_type=chat_type,
        chat_id=chat_id,
        user_id=user_id,
        message_id=message_id,
        reply_to_message_id=reply_to_message_id,
        is_channel_discussion=is_discussion,
        is_channel_post=is_channel_post,
        chat_title=chat_title
    )
    
    logger.info(f"Processing message in session: {session_context.session_id}")
    logger.info(f"  Message details: message_id={message_id}, reply_to={reply_to_message_id}, chat_type={chat_type}")
    
    message_text = None
    transcription_message_id = None
    
    # Handle voice or audio messages
    if update.message.voice or update.message.audio:
        message_text, transcription_message_id = await handle_voice_audio(update, context, chat_id)
        if not message_text:
            return
    
    # Get MCP tools from context for creating user-specific agents
    mcp_tools: MultiMCPTools = get_dependency(context, "mcp_tools")
    
    # Get or create session-specific agent
    ai_agent = get_basic_agent(
        user_id=str(user_id),
        session_id=session_context.session_id,
        mcp_tools=mcp_tools
    )
    
    # Store the agent in bot_data for this session
    context.bot_data[f"agent_{session_context.session_id}"] = ai_agent
    
    # Process images if present
    images = await process_images(update, context) if update.message.photo else None
    
    # Process documents if present
    documents = await process_documents(update, context) if update.message.document else None

    # For channel posts, we skip AI processing since duplication handler takes care of it
    if is_channel_post:
        logger.info("Skipping AI processing for channel post - duplication handler will create a separate message for AI")
        return
    
    # Extract text content
    if message_text is None:
        if update.message.text:
            message_text = update.message.text
        elif update.message.caption:
            message_text = update.message.caption
        elif images:
            message_text = "<User sent an image, wait their next message, answer very shortly>"
        elif documents or update.message.document:
            message_text = "<User sent a document, please analyze it>"
        else:
            logger.warning("No text or caption found in the message")
            message_text = "<No text provided>"
    
    # TODO: Add author context for multi-user channel discussions
    # if is_discussion and not is_channel_post:
    #     user_name = update.effective_user.first_name or "User"
    #     if update.effective_user.username:
    #         user_name += f" (@{update.effective_user.username})"
    #     message_text = f"[Comment by {user_name}]: {message_text}"

    # Determine reply target based on context
    bot_reply_to_message_id = None
    if chat_type == "channel":
        # In channels, always reply to the original post to maintain thread
        if update.message.reply_to_message:
            # If this is a comment, reply to the original post
            bot_reply_to_message_id = update.message.reply_to_message.message_id
        else:
            # If this is a new post, reply to this post
            bot_reply_to_message_id = update.message.message_id
    elif chat_type in ["group", "supergroup"]:
        if is_discussion:
            # In channel discussions, always reply to the original post (not to user comment)
            if update.message.reply_to_message:
                # This is a comment on a post - reply to the original post
                bot_reply_to_message_id = update.message.reply_to_message.message_id
            else:
                # This is a new channel post - reply to this post
                bot_reply_to_message_id = update.message.message_id
        else:
            # Regular groups - reply to the user's message
            bot_reply_to_message_id = update.message.message_id
    else:
        # Private chats and other types - no reply targeting
        bot_reply_to_message_id = None
    
    logger.info(f"  Bot will reply to: {bot_reply_to_message_id}")

    # Pass all processed documents to AI
    final_documents = documents
    
    # Log what we're about to send to AI
    logger.info(f"📤 Sending to AI: text='{message_text[:100]}...', "
               f"images={len(images) if images else 0}, "
               f"documents={len(final_documents) if final_documents else 0}")
    
    # Process the query with user_id for reminders
    asyncio.create_task(ai_agent.process_query(
        message_text, 
        chat_id, 
        context, 
        images,
        final_documents,
        reply_to_message_id=bot_reply_to_message_id,
        user_id=user_id
    ))


async def handle_voice_audio(update: Update, context: ContextTypes.DEFAULT_TYPE, chat_id: int) -> tuple[Optional[str], Optional[int]]:
    """Handle voice or audio messages with transcription."""
    is_voice = update.message.voice is not None
    message_type = "voice" if is_voice else "audio"
    icon = "🎤" if is_voice else "🎵"
    
    # Send a transcribing status message
    status_message = await context.bot.send_message(
        chat_id=chat_id,
        text=icon,
        reply_to_message_id=update.message.message_id
    )
    transcription_message_id = status_message.message_id
    
    # Indicate typing while transcribing
    await context.bot.send_chat_action(chat_id, ChatAction.TYPING)
    
    # Transcribe the voice/audio message
    message_text = await process_voice(update, context)
    
    if not message_text:
        await context.bot.edit_message_text(
            chat_id=chat_id,
            message_id=transcription_message_id,
            text=f"❌ Failed to transcribe {message_type}"
        )
        return None, None
    
    # Delete the transcription status message
    await context.bot.delete_message(chat_id, transcription_message_id)
    
    return message_text, transcription_message_id

async def process_images(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[List[Image]]:
    """Process images from a Telegram message and convert them to agno.media.Image objects."""
    if not update.message.photo:
        return None
    
    images = []
    # Get the largest photo (last item in the list)
    photo = update.message.photo[-1]
    
    try:
        # Get file from Telegram
        file = await context.bot.get_file(photo.file_id)
        
        # Download the file
        image_bytes_io = io.BytesIO()
        await file.download_to_memory(image_bytes_io)
        image_bytes = image_bytes_io.getvalue()
        
        # Create Image object
        image = Image(content=image_bytes)
        images.append(image)
        
        logger.info(f"Processed image with file_id: {photo.file_id}, size: {len(image_bytes)} bytes")
    except Exception as e:
        logger.error(f"Error processing image: {e}", exc_info=True)
    
    return images if images else None


async def process_documents(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[List[File]]:
    """
    Process PDF documents from Telegram using Claude Files API.
    Only PDF and plain text files are supported.
    """
    if not update.message.document:
        logger.debug("🚫 No document found in message")
        return None

    logger.info("🔄 Starting document processing...")
    
    files = []
    document = update.message.document
    
    # Only PDF and plain text are supported
    supported_types = {
        'application/pdf',  # PDF files
        'text/plain',       # Plain text files
    }
    
    try:
        # Get MIME type first
        mime_type = document.mime_type
        
        logger.info(f"📄 Processing document: {document.file_name}, "
                   f"mime_type: {mime_type}, size: {document.file_size} bytes")
        
        # Check for document spam (multiple documents sent quickly)
        import time
        current_time = time.time()
        file_key = f"{document.file_id}"
        
        if file_key in _recent_documents:
            time_diff = current_time - _recent_documents[file_key]
            if time_diff < 3.0:  # If less than 3 seconds since last processing
                logger.info("🚫 Skipping duplicate document processing (processed within 3s)")
                return None
        
        _recent_documents[file_key] = current_time

        # Check file size limit (Claude API: 32MB, 100 pages)
        max_size = 32 * 1024 * 1024  # 32MB limit
        if document.file_size and document.file_size > max_size:
            logger.warning(f"❌ Document too large: {document.file_size} bytes, max: {max_size} bytes")
            return None
        
        logger.info(f"✅ File size OK: {document.file_size} bytes")
        
        # Check if file type is supported
        is_supported = mime_type in supported_types
        
        # Fallback check by file extension if mime type is not recognized
        if not is_supported and document.file_name:
            extension = document.file_name.lower().split('.')[-1]
            logger.info(f"🔍 Checking extension fallback: .{extension}")
            
            if extension in {'pdf', 'txt'}:
                is_supported = True
                logger.info(f"✅ Extension fallback successful: .{extension}")
            else:
                logger.info(f"❌ Extension .{extension} not supported")
        
        if not is_supported:
            logger.warning(f"❌ Unsupported file type: {document.mime_type}, filename: {document.file_name}")
            logger.warning("Only PDF and plain text files are supported")
            return None
        
        # Get file from Telegram
        logger.info(f"⬇️ Downloading file from Telegram...")
        file = await context.bot.get_file(document.file_id)
        
        # Download file content to memory
        import io
        file_bytes_io = io.BytesIO()
        await file.download_to_memory(file_bytes_io)
        file_bytes = file_bytes_io.getvalue()
        logger.info(f"✅ Downloaded {len(file_bytes)} bytes to memory")
        
        # Upload to Claude Files API
        logger.info(f"📤 Uploading to Claude Files API...")
        
        import tempfile
        import os
        
        # Create temporary file for Files API upload
        file_extension = ''
        if document.file_name and '.' in document.file_name:
            file_extension = '.' + document.file_name.split('.')[-1]
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            temp_file.write(file_bytes)
            temp_file_path = temp_file.name
        
        try:
            # Initialize Anthropic client and upload
            anthropic_client = Anthropic(api_key=ANTHROPIC_API_KEY)
            uploaded_file = anthropic_client.beta.files.upload(
                file=open(temp_file_path, 'rb')
            )
            
            logger.info(f"✅ Files API upload successful! File ID: {uploaded_file.id}")
            agno_file = File(external=uploaded_file)
            files.append(agno_file)
            
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
        
        logger.info(f"🎉 Document processing complete: {document.file_name}")
        
    except Exception as e:
        logger.error(f"Error processing document: {e}", exc_info=True)
    
    return files if files else None

async def process_voice(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[str]:
    """
    Process voice or audio message from Telegram and transcribe it using Deepgram.
    
    Args:
        update: The Telegram update
        context: The Telegram context
        
    Returns:
        Transcribed text or None if transcription failed
    """
    # Check if we have a voice or audio message
    voice = update.message.voice
    audio = update.message.audio
    
    if not voice and not audio:
        return None
    
    media_type = "voice" if voice else "audio"
    
    try:
        # Get file details based on message type
        if voice:
            media = voice
        else:
            media = audio
            
        # Set a timeout for the download operation
        try:
            file = await asyncio.wait_for(
                context.bot.get_file(media.file_id),
                timeout=15.0  # 15 second timeout
            )
        except asyncio.TimeoutError:
            logger.error(f"Timeout downloading {media_type} file from Telegram")
            return None
            
        # Download the file with timeout
        try:
            media_bytes_io = io.BytesIO()
            await asyncio.wait_for(
                file.download_to_memory(media_bytes_io),
                timeout=30.0  # 30 second timeout for download
            )
            media_bytes = media_bytes_io.getvalue()
        except asyncio.TimeoutError:
            logger.error(f"Timeout downloading {media_type} content to memory")
            return None
        
        duration = getattr(media, 'duration', 0)
        logger.info(f"Processing {media_type} message with file_id: {media.file_id}, duration: {duration}s, size: {len(media_bytes)} bytes")
        
        # Transcribe the audio
        if media_bytes:
            # Determine language based on user settings (can be extended later)
            language = "ru"  # Default to Russian based on your requirements
            
            # Transcribe using Deepgram with timeout
            try:
                transcribed_text = await asyncio.wait_for(
                    speech_to_text_service.transcribe_audio(media_bytes, language),
                    timeout=30.0  # 30 second timeout for transcription
                )
                
                if transcribed_text:
                    logger.info(f"Transcription successful: '{transcribed_text[:50]}...'")
                    return transcribed_text
                else:
                    logger.warning("Transcription failed or returned empty result")
                    return None
            except asyncio.TimeoutError:
                logger.error("Timeout during audio transcription")
                return None
        else:
            logger.warning(f"Downloaded {media_type} file is empty")
            return None
            
    except telegram.error.TimedOut:
        logger.error(f"Telegram API timeout while processing {media_type} message")
        return None
    except Exception as e:
        logger.error(f"Error processing {media_type} message: {e}", exc_info=True)
        return None


# Store recently processed photos to avoid spam
_recent_photos = {}

# Store recently processed documents to avoid spam
_recent_documents = {}

async def handle_channel_post_duplication(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[telegram.Message]:
    """
    Handle channel post duplication logic:
    1. Edit original channel post to "1"
    2. Duplicate original content to discussion as a comment with author marking
    
    Returns:
        The duplicated message object if successful, None otherwise
    """
    if not (update.message and update.message.forward_origin and 
            hasattr(update.message.forward_origin, 'chat') and
            hasattr(update.message.forward_origin, 'message_id')):
        logger.warning("Cannot handle post duplication: missing forward origin info")
        return None
    
    # Extract original post information
    original_channel_id = update.message.forward_origin.chat.id
    original_message_id = update.message.forward_origin.message_id
    
    # Handle different message types
    original_text = None
    message_text_for_ai = None
    
    # Handle voice/audio messages
    if update.message.voice or update.message.audio:
        logger.info("🎤 Processing voice/audio in channel post")
        original_text, _ = await handle_voice_audio(update, context, update.effective_chat.id)
        message_text_for_ai = original_text
    # Handle photos
    elif update.message.photo:
        logger.info("📸 Processing photo in channel post")
        
        # Check for photo spam (multiple photos sent quickly)
        import time
        current_time = time.time()
        chat_key = f"{original_channel_id}_{update.effective_user.id}"
        
        if chat_key in _recent_photos:
            time_diff = current_time - _recent_photos[chat_key]
            if time_diff < 3.0:  # If less than 3 seconds since last photo
                logger.info("🚫 Skipping duplicate photo processing (sent within 3s)")
                return None
        
        _recent_photos[chat_key] = current_time
        
        original_text = update.message.caption or ""
        message_text_for_ai = original_text if original_text else "<User sent an image>"
    # Handle documents
    elif update.message.document:
        logger.info("📄 Processing document in channel post")
        original_text = update.message.caption or ""
        message_text_for_ai = original_text if original_text else "<User sent a document>"
    # Handle text
    elif update.message.text:
        original_text = update.message.text
        message_text_for_ai = original_text
    
    if not original_text or not original_text.strip():
        logger.info("⚠️ No text content for title generation, but processing anyway")
        original_text = ""
        if not message_text_for_ai:
            message_text_for_ai = "<Media message>"
    
    logger.info(f"Handling post duplication: channel_id={original_channel_id}, message_id={original_message_id}")
    
    try:
        # Step 1: Generate title for the post if we have text
        if original_text and original_text.strip():
            generated_title = await title_generator.generate_title(original_text)
            final_title = generated_title if generated_title else "1"
            
            # Edit original post to generated title - use appropriate method for media
            if update.message.photo or update.message.voice or update.message.audio or update.message.document:
                await context.bot.edit_message_caption(
                    chat_id=original_channel_id,
                    message_id=original_message_id,
                    caption=final_title
                )
            else:
                await context.bot.edit_message_text(
                    chat_id=original_channel_id,
                    message_id=original_message_id,
                    text=final_title
                )
            logger.info(f"Successfully edited original post {original_message_id} to '{final_title}'")
        
        # Step 2: Send duplicated content to discussion with author marking
        discussion_chat_id = update.effective_chat.id
        author_mark = "👤"  # User icon to mark original author
        
        # Only duplicate if we have meaningful text content
        # Skip duplication for media without captions to avoid placeholder spam
        if ((update.message.photo and not update.message.caption) or 
            (update.message.document and not update.message.caption)):
            logger.info("📸📄 Skipping duplication for media without caption - processing directly")
            # Create a fake duplicated message for AI processing
            class FakeDuplicatedMessage:
                def __init__(self):
                    if update.message.photo:
                        self.text = f"{author_mark} <User sent an image>"
                    elif update.message.document:
                        self.text = f"{author_mark} <User sent a document>"
                    else:
                        self.text = f"{author_mark} <Media message>"
                    self.message_id = update.message.message_id
            
            return FakeDuplicatedMessage()
        
        # Use message_text_for_ai for duplication (includes transcribed text)
        if message_text_for_ai and message_text_for_ai.strip() and message_text_for_ai != "<User sent an image>":
            # Capitalize first letter after emoji
            capitalized_text = message_text_for_ai[0].upper() + message_text_for_ai[1:] if message_text_for_ai else ""
            duplicated_text = f"{author_mark} {capitalized_text}"
        else:
            duplicated_text = f"{author_mark} Media message"
        
        # Send the duplicated message as a reply to the forwarded post
        duplicated_message = await context.bot.send_message(
            chat_id=discussion_chat_id,
            text=duplicated_text,
            reply_to_message_id=update.message.message_id
        )
        
        logger.info(f"Successfully duplicated content to discussion chat {discussion_chat_id}, message_id={duplicated_message.message_id}")
        
        return duplicated_message
        
    except Exception as e:
        logger.error(f"Error in post duplication: {e}", exc_info=True)
        # If editing fails, we still continue with normal processing
        # This ensures the bot doesn't break if it lacks admin permissions
        return None


async def process_duplicated_channel_post(update: Update, context: ContextTypes.DEFAULT_TYPE, duplicated_message: telegram.Message) -> None:
    """
    Process the duplicated channel post message through AI agent
    """
    if not update.effective_chat or not update.effective_user:
        return
    
    chat_id = update.effective_chat.id
    
    # For channel discussions, use admin's user_id from mapping
    admin_user_id = CHANNEL_ADMIN_MAP.get(chat_id)
    if not admin_user_id:
        logger.warning(f"No admin mapping found for chat {chat_id}, skipping AI processing")
        return
    
    user_id = admin_user_id
    logger.info(f"Processing duplicated post with admin user_id {admin_user_id} for chat {chat_id}")
    
    # Create session context for the duplicated message
    chat_title = update.effective_chat.title or update.effective_chat.first_name or f"Chat {chat_id}"
    session_context = session_manager.get_session_context(
        chat_type=update.effective_chat.type,
        chat_id=chat_id,
        user_id=user_id,
        message_id=duplicated_message.message_id,
        reply_to_message_id=update.message.message_id,  # Original forwarded message
        is_channel_discussion=True,
        is_channel_post=False,  # This is now a comment, not the original post
        chat_title=chat_title
    )
    
    logger.info(f"Processing duplicated message in session: {session_context.session_id}")
    
    # Get MCP tools and create session-specific agent
    from src.telegram.types.context import get_dependency
    mcp_tools: MultiMCPTools = get_dependency(context, "mcp_tools")
    
    ai_agent = get_basic_agent(
        user_id=str(user_id),
        session_id=session_context.session_id,
        mcp_tools=mcp_tools
    )
    
    # Store the agent in bot_data for this session
    context.bot_data[f"agent_{session_context.session_id}"] = ai_agent
    
    # Extract the original text (remove the 👤 prefix)
    message_text = duplicated_message.text or ""
    if message_text.startswith("👤 "):
        message_text = message_text[2:]  # Remove "👤 " prefix
    
    # Process images if the original message had photos
    images = await process_images(update, context) if update.message.photo else None
    
    # Process documents if the original message had documents
    documents = await process_documents(update, context) if update.message.document else None
    
    # Process the query with user_id for reminders
    asyncio.create_task(ai_agent.process_query(
        message_text, 
        chat_id, 
        context, 
        images,    # Now supports images!
        documents, # Now supports documents!
        reply_to_message_id=update.message.message_id,  # Reply to original forwarded message
        user_id=user_id
    ))


async def handle_direct_channel_post(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle direct channel posts with universal message type support."""
    if not update.message or not update.effective_chat or not update.effective_user:
        return

    chat_id = update.effective_chat.id
    message_id = update.message.message_id
    user_id = update.effective_user.id

    # Get admin user_id from mapping for single-user operation
    admin_user_id = CHANNEL_ADMIN_MAP.get(chat_id, user_id)
    logger.info(f"🚀 HANDLING direct channel post from {user_id}, using admin_user_id {admin_user_id}")
    logger.info(f"📝 Message types: text={bool(update.message.text)}, photo={bool(update.message.photo)}, voice={bool(update.message.voice)}, audio={bool(update.message.audio)}")

    message_text = None
    images = None
    files = None
    should_generate_title = False

    # Handle voice/audio messages
    if update.message.voice or update.message.audio:
        message_text, _ = await handle_voice_audio(update, context, chat_id)
        if message_text:
            should_generate_title = True
            logger.info(f"Voice transcribed: '{message_text[:50]}...'")
    
    # Handle photos
    elif update.message.photo:
        images = await process_images(update, context)
        if update.message.caption:
            message_text = update.message.caption
            should_generate_title = True
            logger.info(f"Photo with caption: '{message_text[:50]}...'")
        else:
            message_text = "<User sent an image>"
            logger.info("Photo without caption - skip title generation")
    
    # Handle documents
    elif update.message.document:
        files = await process_documents(update, context)
        if update.message.caption:
            message_text = update.message.caption
            should_generate_title = True
            logger.info(f"Document with caption: '{message_text[:50]}...'")
        else:
            message_text = "<User sent a document>"
            should_generate_title = True
            logger.info("Document without caption - will generate title")
    
    # Handle text messages
    elif update.message.text:
        message_text = update.message.text
        should_generate_title = True
        logger.info(f"Text: '{message_text[:50]}...'")

    if not message_text:
        return

    # Generate title and edit post if needed
    if should_generate_title:
        try:
            generated_title = await title_generator.generate_title(message_text)
            final_title = generated_title or "1"
            
            # For media messages (photos, voice, documents), edit caption; for text, edit text
            if update.message.photo or update.message.voice or update.message.audio or update.message.document:
                await context.bot.edit_message_caption(
                    chat_id=chat_id,
                    message_id=message_id,
                    caption=final_title
                )
            else:
                await context.bot.edit_message_text(
                    chat_id=chat_id,
                    message_id=message_id,
                    text=final_title
                )
            logger.info(f"Edited post {message_id} to '{final_title}'")
        except Exception as e:
            logger.error(f"Failed to edit post: {e}")

    # Create AI session and process
    chat_title = update.effective_chat.title or update.effective_chat.first_name or f"Chat {chat_id}"
    session_context = session_manager.get_session_context(
        chat_type="channel",
        chat_id=chat_id,
        user_id=admin_user_id,
        message_id=message_id,
        reply_to_message_id=None,
        is_channel_discussion=False,
        is_channel_post=True,
        chat_title=chat_title
    )

    logger.info(f"Session: {session_context.session_id}")

    mcp_tools: MultiMCPTools = get_dependency(context, "mcp_tools")
    ai_agent = get_basic_agent(
        user_id=str(admin_user_id),
        session_id=session_context.session_id,
        mcp_tools=mcp_tools
    )

    context.bot_data[f"agent_{session_context.session_id}"] = ai_agent

    # Process through AI
    asyncio.create_task(ai_agent.process_query(
        message_text,
        chat_id,
        context,
        images,
        files,
        reply_to_message_id=message_id,
        user_id=admin_user_id
    ))
