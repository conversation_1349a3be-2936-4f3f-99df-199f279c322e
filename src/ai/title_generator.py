import logging
from typing import Optional

from agno.agent import Agent
from agno.models.anthropic import Claude
from agno.models.openai import OpenAIChat

from src.utils.config import OPENAI_API_KEY, ANTHROPIC_API_KEY

logger = logging.getLogger(__name__)


class TitleGenerator:
    """
    Simple agent for generating conversation titles from user messages.
    Compresses user text to 2-5 words to create meaningful conversation titles.
    """
    
    def __init__(self):
        """Initialize the title generator with minimal configuration."""
        self.agent = Agent(
            agent_id="title_generator",
            model=<PERSON>(id="claude-3-5-haiku-latest", api_key=ANTHROPIC_API_KEY, max_tokens=20),
            instructions=[
                "You are a title generator for conversations.",
                "Your task is to compress user messages into short, meaningful titles with relevant emojis.",
                "Rules:",
                "- Start with ONE relevant emoji followed by a space",
                "- Then add exactly 2-5 words that capture the main topic",
                "- Choose diverse, contextually appropriate emojis",
                "- Use simple, clear language for the text part",
                "- No punctuation or special characters in text",
                "- Return only the emoji + title, nothing else",
                "- Be creative and match emoji to content theme",
                "- Choose diverse emojis to avoid repetition",
                "- First letter of first word should be capitalized",
                "- Must be friendly and engaging, not robotic",
                "- In english"
            ],
            add_datetime_to_instructions=True,
            debug_mode=False
        )
    
    async def generate_title(self, user_message: str) -> Optional[str]:
        """
        Generate a short title from user message.
        
        Args:
            user_message: The original user message text
            
        Returns:
            Generated title (2-5 words) or None if generation failed
        """
        if not user_message or not user_message.strip():
            return None
            
        try:
            logger.info(f"Generating title for message: {user_message[:50]}...")
            
            # Use agent to generate title
            response = await self.agent.arun(
                f"Generate a title for this message: {user_message}",
                stream=False
            )
            
            if response and response.content:
                title = response.content.strip()
                
                # Validate title length (2-5 words)
                word_count = len(title.split())
                if 2 <= word_count <= 5:
                    logger.info(f"Generated title: '{title}' ({word_count} words)")
                    return title
                else:
                    logger.warning(f"Generated title has {word_count} words, expected 2-5: '{title}'")
                    # Return first 5 words if too long
                    words = title.split()[:5]
                    if len(words) >= 2:
                        return " ".join(words)
            
            logger.warning("Failed to generate valid title")
            return None
            
        except Exception as e:
            logger.error(f"Error generating title: {e}", exc_info=True)
            return None


# Global instance for easy access
title_generator = TitleGenerator()