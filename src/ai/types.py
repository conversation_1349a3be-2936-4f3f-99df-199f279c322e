from dataclasses import dataclass
from enum import StrEnum
from typing import Optional


class ApiErrorType(StrEnum):
    OVERLOADED = "overloaded_error"

@dataclass
class ToolCall:
    """Class to track individual tool calls"""

    tool_name: str
    tool_call_id: str
    finished: bool = False
    is_error: bool = False

    # Track if this tool call has been rendered in the message
    component_index: Optional[int] = None

    def get_key(self) -> str:
        return f"{self.tool_call_id}:{self.finished}"
