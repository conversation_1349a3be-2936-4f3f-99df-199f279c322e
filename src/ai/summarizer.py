import logging
import time
from copy import deepcopy
from dataclasses import dataclass
from datetime import datetime
from textwrap import dedent
from typing import List, Optional, cast, Dict, Union

from agno.memory.v2 import SessionSummary, Memory, MemoryManager, SessionSummarizer, UserMemory
from agno.memory.v2.db.base import MemoryDb
from agno.models.base import Model
from agno.models.message import Message
from agno.run.response import RunResponse
from agno.run.team import TeamRunResponse

logger = logging.getLogger(__name__)

HISTORY_RUNS_KEEP = 15  # How many last messages dont need to be summarized


# Temporary solution, need to find something better.
# For now it cause problem when some messages (that are not summarized yet, and are not in the last 3 messages) just not included anywhere
@dataclass
class LiteMemory(Memory):
    TOKENS_COUNT = 4000
    # session_id -> [messages.created_at] # Lok i dont find better way to indentificate this
    SUMMARIZED_MESSAGES = {}

    def __init__(
            self,
            model: Optional[Model] = None,
            memory_manager: Optional[MemoryManager] = None,
            summarizer: Optional[SessionSummarizer] = None,
            db: Optional[MemoryDb] = None,
            memories: Optional[Dict[str, Dict[str, UserMemory]]] = None,
            summaries: Optional[Dict[str, Dict[str, SessionSummary]]] = None,
            runs: Optional[Dict[str, List[Union[RunResponse, TeamRunResponse]]]] = None,
            debug_mode: bool = False,
            delete_memories: bool = False,
            clear_memories: bool = False,
    ):
        super().__init__(
            model=model,
            memory_manager=memory_manager,
            summarizer=summarizer,
            db=db,
            memories=memories,
            summaries=summaries,
            runs=runs,
            debug_mode=debug_mode,
            delete_memories=delete_memories,
            clear_memories=clear_memories,
        )

    def create_session_summary(self, session_id: str, user_id: Optional[str] = None) -> Optional[SessionSummary]:
        raise ValueError("Summarizer not initialized")

    async def acreate_session_summary(self, session_id: str, user_id: Optional[str] = None) -> Optional[SessionSummary]:

        self.set_log_level()

        if user_id is None:
            user_id = "default"

        # Messages without last RAW_BACKLOG messages
        if HISTORY_RUNS_KEEP <= 1:
            raise ValueError("HISTORY_RUNS_KEEP must be greater than 1")

        # Get all messages
        messages = self.get_messages_for_session(session_id=session_id, skip_history_messages=True)
        messages = messages[:-(HISTORY_RUNS_KEEP * 2)] # Need * 2 to be using the same length as Agent (num_history_runs)

        # Filter out messages that have already been summarized
        messages = [
            message for message in messages if message.created_at not in self.SUMMARIZED_MESSAGES.get(session_id, [])
        ]

        total_tokens = sum(message.metrics.total_tokens for message in messages)

        if total_tokens < self.TOKENS_COUNT:
            logger.info(f"Chat {user_id}: Not enough tokens to summarize. Total tokens: {total_tokens}")

            return None

        if total_tokens > 50000:
            # TODO: Maybe split and take last messages that we can?
            logger.info(f"Chat {user_id}: Too many tokens to summarize. Total tokens: {total_tokens}")

            return None

        # current summary
        current_summary = self.get_session_summary(session_id=session_id, user_id=user_id)

        if current_summary is not None:
            messages.append(
                Message(
                    role="user",
                    content=dedent(f"""
                        The current summary is: {current_summary.summary}
                        """),
                    created_at=int(time.time()),
                )
            )

        summary_response = await self.summary_manager.arun(conversation=messages)
        if summary_response is None:
            return None
        session_summary = SessionSummary(
            summary=summary_response.summary, topics=summary_response.topics, last_updated=datetime.now()
        )
        self.summaries.setdefault(user_id, {})[session_id] = session_summary  # type: ignore

        # Store the summarized messages
        if session_id not in self.SUMMARIZED_MESSAGES:
            self.SUMMARIZED_MESSAGES[session_id] = []

        self.SUMMARIZED_MESSAGES[session_id].extend(
            message.created_at for message in messages
        )

        return session_summary
