# Auto-Deployment Setup

Этот проект настроен для автоматического деплоя на VPS через GitHub Actions.

## 🚀 Как это работает

1. **Push в main/master** → GitHub Actions → SSH на VPS → `make update`
2. **Минимальный downtime** - билд происходит, потом быстрая замена контейнера
3. **Ручной запуск** - можно запустить деплой вручную через GitHub Actions

## 🔧 Настройка на VPS

### 1. Создай SSH ключ на VPS

```bash
# На VPS создай SSH ключ для GitHub Actions
ssh-keygen -t ed25519 -C "github-actions" -f ~/.ssh/github-actions
cat ~/.ssh/github-actions.pub >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/github-actions
chmod 644 ~/.ssh/github-actions.pub

# Покажи приватный ключ (скопируй его)
cat ~/.ssh/github-actions
```

### 2. Настрой GitHub Secrets

Иди в GitHub: **Settings** → **Secrets and variables** → **Actions** → **New repository secret**

Добавь эти секреты:

| Secret Name | Value | Пример |
|-------------|-------|---------|
| `VPS_HOST` | IP адрес твоего VPS | `123.456.789.012` |
| `VPS_USER` | Пользователь для SSH | `root` |
| `VPS_SSH_KEY` | Приватный SSH ключ | Содержимое `~/.ssh/github-actions` |
| `VPS_PORT` | SSH порт (опционально) | `22` |
| `VPS_PROJECT_PATH` | Путь к проекту | `/root/telegram-mcp-client` |

### 3. Первоначальная настройка проекта на VPS

```bash
# Клонируй проект
cd /root
git clone https://github.com/твой-username/telegram-mcp-client.git
cd telegram-mcp-client

# Настрой окружение
make setup
nano .env  # добавь свои API ключи

# Первый запуск
make up
```

## 🎯 Команды деплоя

### Автоматический деплой
- **Push в main/master** - автоматически запускает деплой
- **Manual trigger** - в GitHub Actions можно запустить вручную

### Ручной деплой на VPS
```bash
# Обновление с минимальным downtime (рекомендуется)
make update

# Обновление с полной остановкой
make update-with-downtime
```

## 🔍 Мониторинг деплоя

### На GitHub
- Иди в **Actions** tab в репозитории
- Смотри статус деплоя в реальном времени

### На VPS
```bash
# Проверь статус
make status

# Посмотри логи
make logs

# Проверь здоровье
make health
```

## 🛠️ Troubleshooting

### Деплой не работает
1. **Проверь SSH ключи**: `ssh -i ~/.ssh/github-actions root@твой-ip`
2. **Проверь секреты** в GitHub Settings
3. **Проверь логи** в GitHub Actions

### Ошибки на VPS
```bash
# Проверь статус Docker
docker ps

# Проверь логи приложения
make logs

# Перезапусти если нужно
make restart
```

### SSH проблемы
```bash
# Проверь SSH конфиг
cat ~/.ssh/authorized_keys | grep github-actions

# Проверь права
ls -la ~/.ssh/
```

## 🔒 Безопасность

1. **SSH ключ только для деплоя** - создан отдельный ключ
2. **Ограниченные права** - ключ может только запускать команды деплоя
3. **Секреты в GitHub** - приватные данные не в коде

## 📋 Workflow файл

Файл `.github/workflows/deploy.yml` содержит:
- Триггер на push в main/master
- SSH подключение к VPS
- Выполнение `make update`
- Логирование результата

## 🎉 Готово!

Теперь каждый push в main автоматически обновляет бота на VPS с минимальным downtime! 🚀
