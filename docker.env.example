# Docker Environment Configuration for Telegram MCP Client
# Copy this file to .env and fill in your actual values

# =============================================================================
# REQUIRED CONFIGURATION
# =============================================================================

# Telegram Bot Token (get from @BotFather)
TELEGRAM_TOKEN=your_telegram_bot_token_here

# Anthropic API Key (for Claude AI)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# =============================================================================
# AI MODEL CONFIGURATION
# =============================================================================

# Claude model to use (default: claude-3-5-sonnet-latest)
CLAUDE_MODEL=claude-3-5-sonnet-latest

# System prompt for the AI assistant
CLAUDE_SYSTEM_PROMPT=You are a helpful telegram bot assistant

# =============================================================================
# OPTIONAL API KEYS
# =============================================================================

# OpenAI API Key (for memory management and some tools)
OPENAI_API_KEY=your_openai_api_key_here

# OpenRouter API Key (for session summarization)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Deepgram API Key (for speech-to-text functionality)
DEEPGRAM_API_KEY=your_deepgram_api_key_here

# =============================================================================
# MCP TOOLS CONFIGURATION
# =============================================================================

# Todoist API Key (for todoist-mcp tool)
API_KEY=your_todoist_api_key_here

# Brave Search API Key (if using brave-search MCP)
BRAVE_API_KEY=your_brave_search_api_key_here

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Disable telemetry
AGNO_TELEMETRY=false

# Environment (development/production)
ENVIRONMENT=production

# Python configuration
PYTHONUNBUFFERED=1

# =============================================================================
# DOCKER-SPECIFIC CONFIGURATION
# =============================================================================

# Container timezone (set to your local timezone)
TZ=Europe/Warsaw

# Log level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO
