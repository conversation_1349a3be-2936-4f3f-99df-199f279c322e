import asyncio
import signal
import sys
import os

from agno.tools.mcp import MultiMCPTools

from src.ai.agent import get_basic_agent
from src.telegram import TelegramBot
from src.utils.logging import setup_logging
from src.utils.database import db_manager

logger = setup_logging()


def create_shutdown_handler(bot, signal_name):
    async def handler(*args):
        logger.info(f"Received {signal_name}, shutting down...")

        await bot.shutdown()

    return lambda *args: asyncio.create_task(handler(*args))

async def main():
    try:
        async with MultiMCPTools(
            [
                "npx -y todoist-mcp",
                # "node /Users/<USER>/Dev/My/yazio-mcp/dist/index.js",
                # "python3 -m mcp_server_fetch",
                # "npx -y @modelcontextprotocol/server-brave-search",
            ],
            env={
                "API_KEY": os.getenv("API_KEY", ""),
                "BRAVE_API_KEY": os.getenv("BRAVE_API_KEY", ""),
                # "YAZIO_USERNAME": os.getenv("YAZIO_USERNAME", ""),
                # "YAZIO_PASSWORD": os.getenv("YAZIO_PASSWORD", "")
            },
            timeout_seconds=30,
        ) as mcp_tools:
            # Create a default agent for initial setup
            ai_agent = get_basic_agent(
                user_id="default", 
                session_id="default",
                mcp_tools=mcp_tools
            )

            logger.info("AI Agent initialized successfully with memory and MCP tools")

            # Run database migrations after agent initialization
            logger.info("Running database migrations...")
            db_manager.run_migrations()
            logger.info("Database migrations completed")

            bot = TelegramBot(ai_agent, mcp_tools)

            try:
                loop = asyncio.get_running_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            for sig_name in ('SIGINT', 'SIGTERM'):
                if sys.platform == 'win32' and sig_name == 'SIGTERM':
                    continue

                loop.add_signal_handler(
                    getattr(signal, sig_name),
                    create_shutdown_handler(bot, sig_name)
                )

            await bot.run()
    except Exception as e:
        logger.critical(f"Critical error on startup: {e}", exc_info=True)
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)