name: Auto Deploy to VPS

on:
  push:
    branches: [ main, master ]  # Trigger on push to main/master branch
  workflow_dispatch:  # Allow manual trigger

jobs:
  deploy:
    name: Deploy to VPS
    runs-on: ubuntu-latest
    
    steps:
    - name: Deploy to server
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.VPS_HOST }}
        username: ${{ secrets.VPS_USER }}
        key: ${{ secrets.VPS_SSH_KEY }}
        port: ${{ secrets.VPS_PORT || 22 }}
        script: |
          cd ${{ secrets.VPS_PROJECT_PATH || '/root/telegram-mcp-client' }}
          make update
          echo "🎉 Deployment completed successfully!"
